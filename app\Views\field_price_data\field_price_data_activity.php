<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>
<div class="card">
    <h1 class="card-title">🏪 Business Locations</h1>
    <p style="color:#666;margin-bottom:0;">Activity: <strong><?= esc($activity['activity_name']) ?></strong></p>
</div>

<!-- Navigation -->
<div class="card" style="margin-top: 10px;">
    <a href="<?= base_url('field/collect') ?>" class="btn btn-secondary btn-block">← Back to Activities</a>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger"><?= session()->getFlashdata('error') ?></div>
<?php endif; ?>

<?php if (empty($locations)): ?>
    <div class="card">
        <div style="text-align:center;padding:20px;color:#666;">
            <div style="font-size:48px;margin-bottom:10px;">📍</div>
            <h3>No Locations Assigned</h3>
            <p>No business locations are assigned to this activity.</p>
            <a href="<?= base_url('field/collect') ?>" class="btn">← Back to Activities</a>
        </div>
    </div>
<?php else: ?>
    <?php foreach ($locations as $loc): ?>
        <div class="card" style="margin-bottom:12px;">
            <div style="display:flex;justify-content:space-between;align-items:flex-start;">
                <div>
                    <h3 style="margin:0;font-size:16px;color:#333;">
                        <?= esc($loc['business_name']) ?>
                    </h3>
                    <div style="color:#666;font-size:14px;">
                        📌 GPS: <?= esc($loc['gps_coordinates'] ?? 'N/A') ?><br>
                        🔖 Status: <strong><?= esc(ucfirst($loc['location_status'] ?? 'active')) ?></strong>
                    </div>
                </div>
                <div>
                    <a class="btn btn-success" href="<?= base_url('field/collect/activity/' . $activity['id'] . '/location/' . $loc['business_location_id']) ?>">Collect</a>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
<?php endif; ?>

<?= $this->endSection() ?>

