<?php

namespace App\Models;

use CodeIgniter\Model;

class ActivityPriceCollectionDataModel extends Model
{
    protected $table = 'activity_price_collection_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'org_id',
        'business_location_id',
        'activity_id',
        'user_id',
        'item_id',
        'price',
        'remarks',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'org_id' => 'required|integer',
        'business_location_id' => 'required|integer',
        'activity_id' => 'required|integer',
        'user_id' => 'required|integer',
        'item_id' => 'required|integer',
        'price' => 'required|decimal|greater_than[0]',
        'remarks' => 'permit_empty|max_length[65535]',
        'status' => 'required|in_list[active,submitted,approved,redo,cancelled]',
        'status_by' => 'permit_empty|integer',
        'status_remarks' => 'permit_empty|max_length[65535]',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization is required',
            'integer' => 'Invalid organization selection'
        ],
        'business_location_id' => [
            'required' => 'Business location is required',
            'integer' => 'Invalid business location selection'
        ],
        'activity_id' => [
            'required' => 'Activity is required',
            'integer' => 'Invalid activity selection'
        ],
        'user_id' => [
            'required' => 'User is required',
            'integer' => 'Invalid user selection'
        ],
        'item_id' => [
            'required' => 'Item is required',
            'integer' => 'Invalid item selection'
        ],
        'price' => [
            'required' => 'Price is required',
            'decimal' => 'Price must be a valid decimal number',
            'greater_than' => 'Price must be greater than 0'
        ],
        'remarks' => [
            'max_length' => 'Remarks is too long'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be active, submitted, approved, redo, or cancelled'
        ],
        'status_by' => [
            'integer' => 'Invalid user ID for status_by'
        ],
        'created_by' => [
            'integer' => 'Invalid user ID for created_by'
        ],
        'updated_by' => [
            'integer' => 'Invalid user ID for updated_by'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Custom validation to check unique price entry per activity, location, and item
     */
    protected $beforeInsert = ['validateUniquePriceEntry'];
    protected $beforeUpdate = ['validateUniquePriceEntry'];
    
    protected function validateUniquePriceEntry(array $data)
    {
        if (isset($data['data']['activity_id']) && isset($data['data']['business_location_id']) && isset($data['data']['item_id'])) {
            $builder = $this->where('activity_id', $data['data']['activity_id'])
                           ->where('business_location_id', $data['data']['business_location_id'])
                           ->where('item_id', $data['data']['item_id'])
                           ->where('is_deleted', false);
            
            // For updates, exclude current record
            if (isset($data['id'])) {
                $builder->where('id !=', $data['id']);
            }
            
            if ($builder->countAllResults() > 0) {
                throw new \CodeIgniter\Database\Exceptions\DatabaseException('Price data already exists for this item at this location in this activity');
            }
        }
        
        return $data;
    }
    
    /**
     * Get price collection data by activity
     */
    public function getPriceDataByActivity(int $activityId)
    {
        return $this->select('activity_price_collection_data.*, business_locations.business_name, goods_items.item as item_name, users.username')
                   ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
                   ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
                   ->join('users', 'users.id = activity_price_collection_data.user_id', 'left')
                   ->where('activity_price_collection_data.activity_id', $activityId)
                   ->where('activity_price_collection_data.is_deleted', false)
                   ->orderBy('business_locations.business_name', 'ASC')
                   ->orderBy('goods_items.item', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get price collection data by business location
     */
    public function getPriceDataByLocation(int $businessLocationId)
    {
        return $this->select('activity_price_collection_data.*, activities.activity_name, goods_items.item as item_name, users.username')
                   ->join('activities', 'activities.id = activity_price_collection_data.activity_id', 'left')
                   ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
                   ->join('users', 'users.id = activity_price_collection_data.user_id', 'left')
                   ->where('activity_price_collection_data.business_location_id', $businessLocationId)
                   ->where('activity_price_collection_data.is_deleted', false)
                   ->where('activities.is_deleted', false)
                   ->orderBy('activities.activity_name', 'ASC')
                   ->orderBy('goods_items.item', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get price collection data by user
     */
    public function getPriceDataByUser(int $userId, int $orgId = null)
    {
        $builder = $this->select('activity_price_collection_data.*, activities.activity_name, business_locations.business_name, goods_items.item as item_name')
                       ->join('activities', 'activities.id = activity_price_collection_data.activity_id', 'left')
                       ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
                       ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
                       ->where('activity_price_collection_data.user_id', $userId)
                       ->where('activity_price_collection_data.is_deleted', false)
                       ->where('activities.is_deleted', false);
        
        if ($orgId) {
            $builder->where('activity_price_collection_data.org_id', $orgId);
        }
        
        return $builder->orderBy('activity_price_collection_data.created_at', 'DESC')
                      ->findAll();
    }
    
    /**
     * Get price collection data by item
     */
    public function getPriceDataByItem(int $itemId, int $orgId = null)
    {
        $builder = $this->select('activity_price_collection_data.*, activities.activity_name, business_locations.business_name, users.username')
                       ->join('activities', 'activities.id = activity_price_collection_data.activity_id', 'left')
                       ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
                       ->join('users', 'users.id = activity_price_collection_data.user_id', 'left')
                       ->where('activity_price_collection_data.item_id', $itemId)
                       ->where('activity_price_collection_data.is_deleted', false)
                       ->where('activities.is_deleted', false);
        
        if ($orgId) {
            $builder->where('activity_price_collection_data.org_id', $orgId);
        }
        
        return $builder->orderBy('activity_price_collection_data.created_at', 'DESC')
                      ->findAll();
    }
    
    /**
     * Get price collection data by status
     */
    public function getPriceDataByStatus(string $status, int $orgId = null)
    {
        $builder = $this->select('activity_price_collection_data.*, activities.activity_name, business_locations.business_name, goods_items.item as item_name, users.username')
                       ->join('activities', 'activities.id = activity_price_collection_data.activity_id', 'left')
                       ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
                       ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
                       ->join('users', 'users.id = activity_price_collection_data.user_id', 'left')
                       ->where('activity_price_collection_data.status', $status)
                       ->where('activity_price_collection_data.is_deleted', false)
                       ->where('activities.is_deleted', false);
        
        if ($orgId) {
            $builder->where('activity_price_collection_data.org_id', $orgId);
        }
        
        return $builder->orderBy('activity_price_collection_data.created_at', 'DESC')
                      ->findAll();
    }
    
    /**
     * Update price collection status
     */
    public function updateStatus(int $priceDataId, string $status, int $userId, string $remarks = null)
    {
        $data = [
            'status' => $status,
            'status_by' => $userId,
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => $userId
        ];
        
        if ($remarks) {
            $data['status_remarks'] = $remarks;
        }
        
        return $this->update($priceDataId, $data);
    }
    
    /**
     * Get price collection statistics by organization
     */
    public function getPriceCollectionStats(int $orgId)
    {
        $stats = [];
        
        // Total price entries
        $stats['total'] = $this->where('org_id', $orgId)
                              ->where('is_deleted', false)
                              ->countAllResults();
        
        // Active entries
        $stats['active'] = $this->where('org_id', $orgId)
                               ->where('status', 'active')
                               ->where('is_deleted', false)
                               ->countAllResults();
        
        // Submitted entries
        $stats['submitted'] = $this->where('org_id', $orgId)
                                  ->where('status', 'submitted')
                                  ->where('is_deleted', false)
                                  ->countAllResults();
        
        // Approved entries
        $stats['approved'] = $this->where('org_id', $orgId)
                                 ->where('status', 'approved')
                                 ->where('is_deleted', false)
                                 ->countAllResults();
        
        return $stats;
    }
    
    /**
     * Get price trends for an item
     */
    public function getPriceTrends(int $itemId, int $businessLocationId = null, int $limit = 10)
    {
        $builder = $this->select('activity_price_collection_data.price, activity_price_collection_data.created_at, activities.activity_name')
                       ->join('activities', 'activities.id = activity_price_collection_data.activity_id', 'left')
                       ->where('activity_price_collection_data.item_id', $itemId)
                       ->where('activity_price_collection_data.status', 'approved')
                       ->where('activity_price_collection_data.is_deleted', false)
                       ->where('activities.is_deleted', false);
        
        if ($businessLocationId) {
            $builder->where('activity_price_collection_data.business_location_id', $businessLocationId);
        }
        
        return $builder->orderBy('activity_price_collection_data.created_at', 'DESC')
                      ->limit($limit)
                      ->findAll();
    }
    
    /**
     * Check if activity has price collection data
     */
    public function hasActivityData(int $activityId): bool
    {
        return $this->where('activity_id', $activityId)
                   ->where('is_deleted', false)
                   ->countAllResults() > 0;
    }

    /**
     * Search price collection data
     */
    public function searchPriceData(int $orgId, string $keyword)
    {
        return $this->select('activity_price_collection_data.*, activities.activity_name, business_locations.business_name, goods_items.item as item_name, users.username')
                   ->join('activities', 'activities.id = activity_price_collection_data.activity_id', 'left')
                   ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
                   ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
                   ->join('users', 'users.id = activity_price_collection_data.user_id', 'left')
                   ->where('activity_price_collection_data.org_id', $orgId)
                   ->groupStart()
                       ->like('activities.activity_name', $keyword)
                       ->orLike('business_locations.business_name', $keyword)
                       ->orLike('goods_items.item', $keyword)
                       ->orLike('activity_price_collection_data.remarks', $keyword)
                   ->groupEnd()
                   ->where('activity_price_collection_data.is_deleted', false)
                   ->where('activities.is_deleted', false)
                   ->orderBy('activity_price_collection_data.created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Count entries by business location and activity
     */
    public function countEntriesByLocationAndActivity(int $businessLocationId, int $activityId)
    {
        return $this->where('business_location_id', $businessLocationId)
                   ->where('activity_id', $activityId)
                   ->where('is_deleted', false)
                   ->countAllResults();
    }

    /**
     * Get entry counts for multiple locations in an activity
     */
    public function getEntryCountsByActivity(int $activityId, array $businessLocationIds = [])
    {
        $builder = $this->select('business_location_id, COUNT(*) as entry_count')
                       ->where('activity_id', $activityId)
                       ->where('is_deleted', false);

        if (!empty($businessLocationIds)) {
            $builder->whereIn('business_location_id', $businessLocationIds);
        }

        return $builder->groupBy('business_location_id')
                      ->findAll();
    }
}
